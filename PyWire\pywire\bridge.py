import os
import webbrowser
import json
import threading
from .server import HTTPServerThread, WSServerThread

class PyWire:
    def __init__(self):
        self.exposed_funcs = {}
        self.http_port = 8000
        self.ws_port = 8001
        self.web_folder = None
        self.clients = []

    # ---------------- Decorator to expose Python functions ----------------
    def expose(self, func):
        self.exposed_funcs[func.__name__] = func
        return func

    # ---------------- Init folder ----------------
    def init(self, folder):
        self.web_folder = folder

    # ---------------- Start PyWire ----------------
    def start(self, page="index.html", http_port=8000, ws_port=8001, folder=None):
        self.http_port = http_port
        self.ws_port = ws_port
        if folder:
            self.web_folder = folder

        if not self.web_folder or not os.path.isdir(self.web_folder):
            raise Exception("Web folder not found")

        url = f"http://127.0.0.1:{self.http_port}/{page}"
        webbrowser.open(url)

        # Start HTTP server
        http_thread = HTTPServerThread(port=self.http_port, folder=self.web_folder)
        http_thread.daemon = True
        http_thread.start()

        # Start WebSocket server
        ws_thread = WSServerThread(port=self.ws_port, bridge=self)
        ws_thread.daemon = True
        ws_thread.start()

        print(f"[PyWire] Running! HTTP: {self.http_port}, WS: {self.ws_port}")
        # Keep main thread alive
        threading.Event().wait()

    # ---------------- Python -> JS call ----------------
    def call_js(self, func, *args):
        msg = json.dumps({"func": func, "args": args})
        for client in self.clients:
            client.send_ws(msg)
