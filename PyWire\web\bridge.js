let socket = new WebSocket("ws://localhost:8001");

const PyWire = {
    callPython: function(func, args = []) {
        return new Promise((resolve, reject) => {
            let msg = {func, args};
            socket.send(JSON.stringify(msg));
            socket.onmessage = (event) => {
                let data = JSON.parse(event.data);
                if ("result" in data) resolve(data.result);
                else reject(data.error);
            };
        });
    },

    _exposedJS: {},

    expose: function(func, name) {
        this._exposedJS[name || func.name] = func;
    },

    _handleCallFromPython: function(msg) {
        let {func, args} = JSON.parse(msg);
        if (this._exposedJS[func]) this._exposedJS[func](...args);
    }
};

socket.onmessage = (event) => PyWire._handleCallFromPython(event.data);
