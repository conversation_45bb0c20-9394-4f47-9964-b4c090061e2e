import os
import socket
import threading
import base64
import hashlib
import json
from http.server import HTT<PERSON>erver, SimpleHTTPRequestHandler

# ---------- HTTP Server ----------
class HTTPServerThread(threading.Thread):
    def __init__(self, port=8000, folder="web"):
        super().__init__()
        self.port = port
        self.folder = folder

    def run(self):
        os.chdir(self.folder)
        server = HTTPServer(("127.0.0.1", self.port), SimpleHTTPRequestHandler)
        print(f"[HTTP] Serving on http://127.0.0.1:{self.port}")
        server.serve_forever()

# ---------- WebSocket Server ----------
class WSClient:
    def __init__(self, conn, addr):
        self.conn = conn
        self.addr = addr

    def send_ws(self, message):
        payload = message.encode()
        header = bytearray([0x81])
        length = len(payload)
        if length < 126:
            header.append(length)
        elif length < (1 << 16):
            header.append(126)
            header.extend(length.to_bytes(2, "big"))
        else:
            header.append(127)
            header.extend(length.to_bytes(8, "big"))
        self.conn.send(header + payload)

class WSServerThread(threading.Thread):
    def __init__(self, port=8001, bridge=None):
        super().__init__()
        self.port = port
        self.bridge = bridge

    def run(self):
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.bind(("127.0.0.1", self.port))
        sock.listen()
        print(f"[WS] WebSocket server listening on ws://127.0.0.1:{self.port}")

        while True:
            client_conn, addr = sock.accept()
            threading.Thread(target=self.handle_client, args=(client_conn, addr), daemon=True).start()

    def handle_client(self, conn, addr):
        client = WSClient(conn, addr)
        self.handshake(conn)
        self.bridge.clients.append(client)

        try:
            while True:
                data = self.recv_ws(conn)
                if not data:
                    break
                msg = json.loads(data)
                func_name = msg.get("func")
                args = msg.get("args", [])
                if func_name in self.bridge.exposed_funcs:
                    result = self.bridge.exposed_funcs[func_name](*args)
                    client.send_ws(json.dumps({"result": result}))
                else:
                    client.send_ws(json.dumps({"error": f"{func_name} not found"}))
        except:
            pass
        self.bridge.clients.remove(client)
        conn.close()

    def handshake(self, client):
        key = client.recv(1024).decode()
        headers = {}
        for line in key.split("\r\n")[1:]:
            if ": " in line:
                k, v = line.split(": ", 1)
                headers[k.lower()] = v
        sec_key = headers.get("sec-websocket-key", "")
        accept = base64.b64encode(hashlib.sha1((sec_key + "258EAFA5-E914-47DA-95CA-C5AB0DC85B11").encode()).digest())
        response = (
            "HTTP/1.1 101 Switching Protocols\r\n"
            "Upgrade: websocket\r\n"
            "Connection: Upgrade\r\n"
            f"Sec-WebSocket-Accept: {accept.decode()}\r\n\r\n"
        )
        client.send(response.encode())

    def recv_ws(self, client):
        try:
            data = client.recv(1024)
            if not data:
                return None
            length = data[1] & 127
            if length == 126:
                length = int.from_bytes(data[2:4], "big")
                mask = data[4:8]
                payload = data[8:8+length]
            elif length == 127:
                length = int.from_bytes(data[2:10], "big")
                mask = data[10:14]
                payload = data[14:14+length]
            else:
                mask = data[2:6]
                payload = data[6:6+length]
            return bytes(b ^ mask[i % 4] for i, b in enumerate(payload)).decode()
        except:
            return None
